/**
 * Google Analytics 4 Types and Interfaces
 */

export interface GAConfig {
    /** Google Analytics 4 Measurement ID (G-XXXXXXXXXX) */
    measurementId: string;
    /** API Secret for Measurement Protocol (server-side tracking) */
    apiSecret?: string;
    /** Enable debug mode for development */
    debug?: boolean;
    /** Enhanced privacy mode */
    privacyMode?: boolean;
    /** Custom domain for gtag.js (optional) */
    customDomain?: string;
    /** Disable automatic page view tracking */
    disableAutoPageView?: boolean;
    /** Cookie settings */
    cookieSettings?: CookieSettings;
}

export interface CookieSettings {
    /** Cookie domain */
    domain?: string;
    /** Cookie expiration in seconds */
    expires?: number;
    /** Secure cookie flag */
    secure?: boolean;
    /** SameSite cookie attribute */
    sameSite?: "strict" | "lax" | "none";
    /** Cookie prefix */
    prefix?: string;
}

export interface PrivacySettings {
    /** User has consented to analytics */
    analyticsConsent: boolean;
    /** User has consented to advertising */
    adConsent: boolean;
    /** Anonymize IP addresses */
    anonymizeIp: boolean;
    /** Respect Do Not Track header */
    respectDnt: boolean;
}

export interface GAEvent {
    /** Event name */
    name: string;
    /** Event parameters */
    parameters?: Record<string, any>;
}

export interface PageViewEvent {
    /** Page title */
    page_title?: string;
    /** Page location (URL) */
    page_location?: string;
    /** Page referrer */
    page_referrer?: string;
    /** Custom parameters */
    custom_parameters?: Record<string, string | number | boolean>;
}

export interface EcommerceEvent {
    /** Transaction ID */
    transaction_id: string;
    /** Currency code (ISO 4217) */
    currency: string;
    /** Transaction value */
    value: number;
    /** Items in the transaction */
    items?: EcommerceItem[];
    /** Additional parameters */
    parameters?: Record<string, string | number | boolean>;
}

export interface EcommerceItem {
    /** Item ID */
    item_id: string;
    /** Item name */
    item_name: string;
    /** Item category */
    item_category?: string;
    /** Item variant */
    item_variant?: string;
    /** Item price */
    price: number;
    /** Item quantity */
    quantity: number;
}

export interface UserProperties {
    /** User ID */
    user_id?: string;
    /** Custom user properties */
    custom_properties?: Record<string, string | number | boolean>;
}

export interface GAClientOptions {
    /** Configuration */
    config: GAConfig;
    /** Privacy settings */
    privacy?: PrivacySettings;
    /** User properties */
    user?: UserProperties;
}

export interface GAServerOptions {
    /** Configuration */
    config: GAConfig;
    /** Client ID for server-side tracking */
    clientId: string;
    /** User properties */
    user?: UserProperties;
}

export interface MeasurementProtocolPayload {
    /** Client ID */
    client_id: string;
    /** User ID (optional) */
    user_id?: string;
    /** Events array */
    events: GAEvent[];
    /** User properties */
    user_properties?: Record<string, { value: string | number | boolean }>;
}

export type GAEventName =
    | "page_view"
    | "login"
    | "sign_up"
    | "purchase"
    | "begin_checkout"
    | "add_to_cart"
    | "remove_from_cart"
    | "view_item"
    | "search"
    | "share"
    | "video_start"
    | "video_complete"
    | "file_download"
    | "form_submit"
    | "scroll"
    | "click"
    | string; // Allow custom event names

export interface ConsentSettings {
    /** Analytics storage consent */
    analytics_storage: "granted" | "denied";
    /** Ad storage consent */
    ad_storage: "granted" | "denied";
    /** Ad user data consent */
    ad_user_data: "granted" | "denied";
    /** Ad personalization consent */
    ad_personalization: "granted" | "denied";
}
