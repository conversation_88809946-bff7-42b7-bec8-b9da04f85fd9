/**
 * Server-side Google Analytics Implementation (Measurement Protocol v4)
 */

import type {
    GAConfig,
    GAEvent,
    PageViewEvent,
    EcommerceEvent,
    GAServerOptions,
    MeasurementProtocolPayload,
    UserProperties,
} from "./types.js";
import { validateGAConfig } from "./config.js";

/**
 * Server-side Google Analytics tracker using Measurement Protocol v4
 */
export class GAServer {
    private config: GAConfig;
    private clientId: string;
    private userProperties: UserProperties;
    private baseUrl = "https://www.google-analytics.com/mp/collect";

    constructor(options: GAServerOptions) {
        this.config = options.config;
        this.clientId = options.clientId;
        this.userProperties = options.user || {};
    }

    /**
     * Initialize server-side tracking
     */
    initialize(): void {
        if (!validateGAConfig(this.config)) {
            throw new Error("Invalid Google Analytics configuration");
        }

        if (!this.config.apiSecret) {
            throw new Error("API Secret is required for server-side tracking");
        }

        if (this.config.debug) {
            this.baseUrl = "https://www.google-analytics.com/debug/mp/collect";
        }
    }

    /**
     * Track page view
     */
    async trackPageView(event: PageViewEvent, request?: Request): Promise<void> {
        const requestInfo = this.extractRequestInfo(request);
        const parameters: Record<string, any> = {
            ...(event.page_title && { page_title: event.page_title }),
            ...(event.page_location && { page_location: event.page_location }),
            ...(event.page_referrer && { page_referrer: event.page_referrer }),
            ...requestInfo,
            ...event.custom_parameters,
        };

        await this.sendEvent({
            name: "page_view",
            parameters,
        });
    }

    /**
     * Track custom event
     */
    async trackEvent(event: GAEvent, request?: Request): Promise<void> {
        const parameters = {
            ...event.parameters,
            ...this.extractRequestInfo(request),
        };

        await this.sendEvent({
            name: event.name,
            parameters,
        });
    }

    /**
     * Track ecommerce event
     */
    async trackEcommerce(event: EcommerceEvent, request?: Request): Promise<void> {
        const requestInfo = this.extractRequestInfo(request);
        const parameters: Record<string, any> = {
            transaction_id: event.transaction_id,
            value: event.value,
            currency: event.currency,
            ...(event.items && { items: event.items }),
            ...requestInfo,
            ...event.parameters,
        };

        await this.sendEvent({
            name: "purchase",
            parameters,
        });
    }

    /**
     * Track multiple events in batch
     */
    async trackBatch(events: GAEvent[], request?: Request): Promise<void> {
        const requestInfo = this.extractRequestInfo(request);

        const eventsWithRequestInfo = events.map((event) => ({
            ...event,
            parameters: {
                ...event.parameters,
                ...requestInfo,
            },
        }));

        await this.sendEvents(eventsWithRequestInfo);
    }

    /**
     * Set user properties
     */
    setUserProperties(properties: Record<string, string | number | boolean>): void {
        this.userProperties.custom_properties = {
            ...this.userProperties.custom_properties,
            ...properties,
        };
    }

    /**
     * Set user ID
     */
    setUserId(userId: string): void {
        this.userProperties.user_id = userId;
    }

    /**
     * Send single event to Google Analytics
     */
    private async sendEvent(event: GAEvent): Promise<void> {
        await this.sendEvents([event]);
    }

    /**
     * Send multiple events to Google Analytics
     */
    private async sendEvents(events: GAEvent[]): Promise<void> {
        const payload: MeasurementProtocolPayload = {
            client_id: this.clientId,
            events,
        };

        if (this.userProperties.user_id) {
            payload.user_id = this.userProperties.user_id;
        }

        if (this.userProperties.custom_properties) {
            payload.user_properties = {};
            for (const [key, value] of Object.entries(this.userProperties.custom_properties)) {
                payload.user_properties[key] = { value };
            }
        }

        const url = `${this.baseUrl}?measurement_id=${this.config.measurementId}&api_secret=${this.config.apiSecret}`;

        try {
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(payload),
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            if (this.config.debug) {
                const responseText = await response.text();
                console.log("[Analytics] Server response:", responseText);
                console.log("[Analytics] Sent payload:", JSON.stringify(payload, null, 2));
            }
        } catch (error) {
            console.error("[Analytics] Failed to send events:", error);
            throw error;
        }
    }

    /**
     * Extract useful information from request
     */
    private extractRequestInfo(request?: Request): Record<string, string> {
        if (!request) return {};

        const info: Record<string, string> = {};

        // Extract URL information
        try {
            const url = new URL(request.url);
            info.page_location = url.href;
            info.page_referrer = request.headers.get("referer") || "";
        } catch (e) {
            // Invalid URL
        }

        // Extract user agent
        const userAgent = request.headers.get("user-agent");
        if (userAgent) {
            info.user_agent = userAgent;
        }

        // Extract IP address (for geolocation)
        const cfConnectingIp = request.headers.get("cf-connecting-ip");
        const xForwardedFor = request.headers.get("x-forwarded-for");
        const xRealIp = request.headers.get("x-real-ip");

        const clientIp = cfConnectingIp || xForwardedFor?.split(",")[0] || xRealIp;
        if (clientIp) {
            info.client_ip = clientIp;
        }

        // Extract language
        const acceptLanguage = request.headers.get("accept-language");
        if (acceptLanguage) {
            const language = acceptLanguage.split(",")[0];
            if (language) {
                info.language = language;
            }
        }

        return info;
    }
}

/**
 * Utility functions for server-side tracking
 */

/**
 * Create GA server instance from environment
 */
export function createGAServerFromEnv(
    env: Record<string, string | undefined>,
    clientId: string,
    user?: UserProperties
): GAServer | null {
    const measurementId = env.GA_MEASUREMENT_ID || env.GOOGLE_ANALYTICS_ID;
    const apiSecret = env.GA_API_SECRET;

    if (!measurementId || !apiSecret) {
        return null;
    }

    const config: GAConfig = {
        measurementId,
        apiSecret,
        debug: env.NODE_ENV === "development" || env.GA_DEBUG === "true",
    };

    return new GAServer({ config, clientId, user });
}

/**
 * Track page view from Remix loader/action
 */
export async function trackPageViewFromRequest(
    gaServer: GAServer,
    request: Request,
    pageTitle?: string
): Promise<void> {
    const url = new URL(request.url);

    await gaServer.trackPageView(
        {
            page_title: pageTitle || url.pathname,
            page_location: url.href,
        },
        request
    );
}

/**
 * Middleware for automatic page view tracking
 */
export function createAnalyticsMiddleware(gaServer: GAServer) {
    return async (request: Request, next: () => Promise<Response>): Promise<Response> => {
        const response = await next();

        // Only track successful GET requests
        if (request.method === "GET" && response.ok) {
            try {
                await trackPageViewFromRequest(gaServer, request);
            } catch (error) {
                console.error("[Analytics] Failed to track page view:", error);
            }
        }

        return response;
    };
}
