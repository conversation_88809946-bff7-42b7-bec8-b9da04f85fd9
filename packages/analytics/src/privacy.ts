/**
 * Privacy and Consent Management for Google Analytics
 */

import type { ConsentSettings, PrivacySettings } from "./types.js";
import { privacyToConsentSettings, isDntEnabled } from "./config.js";

/**
 * Privacy manager for handling user consent and privacy preferences
 */
export class PrivacyManager {
    private consentSettings: ConsentSettings;
    private privacySettings: PrivacySettings;
    private consentCallbacks: Array<(consent: ConsentSettings) => void> = [];

    constructor(initialPrivacy?: Partial<PrivacySettings>) {
        this.privacySettings = {
            analyticsConsent: false,
            adConsent: false,
            anonymizeIp: true,
            respectDnt: true,
            ...initialPrivacy,
        };

        // Respect Do Not Track if enabled
        if (this.privacySettings.respectDnt && isDntEnabled()) {
            this.privacySettings.analyticsConsent = false;
            this.privacySettings.adConsent = false;
        }

        this.consentSettings = privacyToConsentSettings(this.privacySettings);
    }

    /**
     * Update privacy settings
     */
    updatePrivacySettings(settings: Partial<PrivacySettings>): void {
        this.privacySettings = { ...this.privacySettings, ...settings };
        this.consentSettings = privacyToConsentSettings(this.privacySettings);

        // Notify all callbacks
        this.consentCallbacks.forEach((callback) => callback(this.consentSettings));

        // Update gtag consent if available
        if (typeof gtag !== "undefined") {
            gtag("consent", "update", this.consentSettings);
        }
    }

    /**
     * Grant analytics consent
     */
    grantAnalyticsConsent(): void {
        this.updatePrivacySettings({ analyticsConsent: true });
    }

    /**
     * Deny analytics consent
     */
    denyAnalyticsConsent(): void {
        this.updatePrivacySettings({ analyticsConsent: false });
    }

    /**
     * Grant advertising consent
     */
    grantAdConsent(): void {
        this.updatePrivacySettings({ adConsent: true });
    }

    /**
     * Deny advertising consent
     */
    denyAdConsent(): void {
        this.updatePrivacySettings({ adConsent: false });
    }

    /**
     * Get current privacy settings
     */
    getPrivacySettings(): PrivacySettings {
        return { ...this.privacySettings };
    }

    /**
     * Get current consent settings
     */
    getConsentSettings(): ConsentSettings {
        return { ...this.consentSettings };
    }

    /**
     * Check if analytics is allowed
     */
    isAnalyticsAllowed(): boolean {
        return (
            this.privacySettings.analyticsConsent &&
            (!this.privacySettings.respectDnt || !isDntEnabled())
        );
    }

    /**
     * Check if advertising is allowed
     */
    isAdAllowed(): boolean {
        return (
            this.privacySettings.adConsent && (!this.privacySettings.respectDnt || !isDntEnabled())
        );
    }

    /**
     * Register a callback for consent changes
     */
    onConsentChange(callback: (consent: ConsentSettings) => void): () => void {
        this.consentCallbacks.push(callback);

        // Return unsubscribe function
        return () => {
            const index = this.consentCallbacks.indexOf(callback);
            if (index > -1) {
                this.consentCallbacks.splice(index, 1);
            }
        };
    }

    /**
     * Save privacy settings to localStorage
     */
    saveToStorage(key = "_ga_privacy_settings"): void {
        if (typeof localStorage === "undefined") return;

        try {
            localStorage.setItem(key, JSON.stringify(this.privacySettings));
        } catch (e) {
            console.warn("[Analytics] Could not save privacy settings to localStorage");
        }
    }

    /**
     * Load privacy settings from localStorage
     */
    loadFromStorage(key = "_ga_privacy_settings"): void {
        if (typeof localStorage === "undefined") return;

        try {
            const stored = localStorage.getItem(key);
            if (stored) {
                const settings = JSON.parse(stored) as Partial<PrivacySettings>;
                this.updatePrivacySettings(settings);
            }
        } catch (e) {
            console.warn("[Analytics] Could not load privacy settings from localStorage");
        }
    }

    /**
     * Clear privacy settings from storage
     */
    clearStorage(key = "_ga_privacy_settings"): void {
        if (typeof localStorage === "undefined") return;

        try {
            localStorage.removeItem(key);
        } catch (e) {
            console.warn("[Analytics] Could not clear privacy settings from localStorage");
        }
    }
}

/**
 * Cookie consent banner utilities
 */
export class ConsentBanner {
    private banner: HTMLElement | null = null;
    private privacyManager: PrivacyManager;

    constructor(privacyManager: PrivacyManager) {
        this.privacyManager = privacyManager;
    }

    /**
     * Show consent banner
     */
    show(options?: {
        message?: string;
        acceptText?: string;
        declineText?: string;
        position?: "top" | "bottom";
    }): void {
        if (this.banner || typeof document === "undefined") return;

        const {
            message = "We use cookies to improve your experience and analyze site usage.",
            acceptText = "Accept",
            declineText = "Decline",
            position = "bottom",
        } = options || {};

        this.banner = document.createElement("div");
        this.banner.className = `ga-consent-banner ga-consent-banner--${position}`;
        this.banner.innerHTML = `
      <div class="ga-consent-banner__content">
        <p class="ga-consent-banner__message">${message}</p>
        <div class="ga-consent-banner__actions">
          <button class="ga-consent-banner__button ga-consent-banner__button--accept">${acceptText}</button>
          <button class="ga-consent-banner__button ga-consent-banner__button--decline">${declineText}</button>
        </div>
      </div>
    `;

        // Add event listeners
        const acceptBtn = this.banner.querySelector(".ga-consent-banner__button--accept");
        const declineBtn = this.banner.querySelector(".ga-consent-banner__button--decline");

        acceptBtn?.addEventListener("click", () => {
            this.privacyManager.grantAnalyticsConsent();
            this.privacyManager.saveToStorage();
            this.hide();
        });

        declineBtn?.addEventListener("click", () => {
            this.privacyManager.denyAnalyticsConsent();
            this.privacyManager.saveToStorage();
            this.hide();
        });

        document.body.appendChild(this.banner);
    }

    /**
     * Hide consent banner
     */
    hide(): void {
        if (this.banner && this.banner.parentNode) {
            this.banner.parentNode.removeChild(this.banner);
            this.banner = null;
        }
    }

    /**
     * Check if consent is needed
     */
    isConsentNeeded(): boolean {
        // Check if user has already made a choice
        if (typeof localStorage === "undefined") return true;

        try {
            const stored = localStorage.getItem("_ga_privacy_settings");
            return !stored;
        } catch (e) {
            return true;
        }
    }
}

// Global gtag function declaration
declare global {
    function gtag(...args: any[]): void;
}
