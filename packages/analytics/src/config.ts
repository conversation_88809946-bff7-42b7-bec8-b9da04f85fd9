/**
 * Google Analytics Configuration Utilities
 */

import type { GAConfig, PrivacySettings, ConsentSettings } from "./types.js";

/**
 * Default Google Analytics configuration
 */
export const DEFAULT_GA_CONFIG: Partial<GAConfig> = {
    debug: false,
    privacyMode: true,
    disableAutoPageView: false,
    cookieSettings: {
        expires: 63072000, // 2 years in seconds
        secure: true,
        sameSite: "lax",
    },
};

/**
 * Default privacy settings (privacy-first approach)
 */
export const DEFAULT_PRIVACY_SETTINGS: PrivacySettings = {
    analyticsConsent: false, // Require explicit consent
    adConsent: false,
    anonymizeIp: true,
    respectDnt: true,
};

/**
 * Default consent settings (denied by default)
 */
export const DEFAULT_CONSENT_SETTINGS: ConsentSettings = {
    analytics_storage: "denied",
    ad_storage: "denied",
    ad_user_data: "denied",
    ad_personalization: "denied",
};

/**
 * Create GA configuration from environment variables
 */
export function createGAConfigFromEnv(env: Record<string, string | undefined>): GAConfig | null {
    const measurementId = env.GA_MEASUREMENT_ID || env.GOOGLE_ANALYTICS_ID;

    if (!measurementId) {
        return null;
    }

    return {
        measurementId,
        apiSecret: env.GA_API_SECRET,
        debug: env.NODE_ENV === "development" || env.GA_DEBUG === "true",
        privacyMode: env.GA_PRIVACY_MODE !== "false",
        customDomain: env.GA_CUSTOM_DOMAIN,
        disableAutoPageView: env.GA_DISABLE_AUTO_PAGE_VIEW === "true",
        ...DEFAULT_GA_CONFIG,
    };
}

/**
 * Validate GA configuration
 */
export function validateGAConfig(config: GAConfig): boolean {
    if (!config.measurementId) {
        console.warn("[Analytics] Missing measurement ID");
        return false;
    }

    if (!config.measurementId.startsWith("G-")) {
        console.warn("[Analytics] Invalid measurement ID format. Expected G-XXXXXXXXXX");
        return false;
    }

    return true;
}

/**
 * Get privacy settings from user preferences
 */
export function getPrivacySettings(userConsent?: Partial<PrivacySettings>): PrivacySettings {
    return {
        ...DEFAULT_PRIVACY_SETTINGS,
        ...userConsent,
    };
}

/**
 * Convert privacy settings to consent settings
 */
export function privacyToConsentSettings(privacy: PrivacySettings): ConsentSettings {
    return {
        analytics_storage: privacy.analyticsConsent ? "granted" : "denied",
        ad_storage: privacy.adConsent ? "granted" : "denied",
        ad_user_data: privacy.adConsent ? "granted" : "denied",
        ad_personalization: privacy.adConsent ? "granted" : "denied",
    };
}

/**
 * Check if Do Not Track is enabled
 */
export function isDntEnabled(): boolean {
    if (typeof navigator === "undefined") return false;
    return (
        navigator.doNotTrack === "1" ||
        (navigator as any).msDoNotTrack === "1" ||
        (window as any).doNotTrack === "1"
    );
}

/**
 * Generate a client ID for server-side tracking
 */
export function generateClientId(): string {
    // Generate a UUID v4-like string for client ID
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}

/**
 * Get or create client ID from storage
 */
export function getClientId(storageKey = "_ga_client_id"): string {
    if (typeof localStorage === "undefined") {
        return generateClientId();
    }

    let clientId = localStorage.getItem(storageKey);
    if (!clientId) {
        clientId = generateClientId();
        try {
            localStorage.setItem(storageKey, clientId);
        } catch (e) {
            // Storage might be disabled
            console.warn("[Analytics] Could not save client ID to localStorage");
        }
    }

    return clientId;
}
