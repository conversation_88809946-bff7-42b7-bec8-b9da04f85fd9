{"name": "@repo/analytics", "version": "0.1.0", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./client": "./dist/client.js", "./server": "./dist/server.js", "./privacy": "./dist/privacy.js", "./config": "./dist/config.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/shared": "workspace:*"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.2"}}