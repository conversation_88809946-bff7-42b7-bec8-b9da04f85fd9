# @repo/analytics

A comprehensive Google Analytics 4 implementation for Remix + Cloudflare Workers with privacy-first approach and full TypeScript support.

## Features

- 🔒 **Privacy-first**: GDPR/CCPA compliant by default with consent management
- 🌐 **Universal**: Works in both client-side (browser) and server-side (Cloudflare Workers) environments
- 📊 **Complete GA4 support**: Page views, custom events, e-commerce tracking, and more
- 🎯 **TypeScript**: Full type safety with comprehensive interfaces
- ⚡ **Performance**: Lightweight, tree-shakeable, minimal runtime impact
- 🛡️ **Secure**: No sensitive data exposure, proper API key handling

## Installation

This package is already installed as part of the monorepo workspace.

## Configuration

### Environment Variables

Add these to your `.dev.vars` file for local development:

```bash
# Google Analytics 4 Measurement ID (required)
GA_MEASUREMENT_ID=G-XXXXXXXXXX

# API Secret for server-side tracking (required for server-side)
GA_API_SECRET=your-measurement-protocol-api-secret

# Optional: Enable debug mode in development
GA_DEBUG=true

# Optional: Custom domain for gtag.js
GA_CUSTOM_DOMAIN=your-custom-domain.com
```

### Getting Your GA4 Credentials

1. **Measurement ID**: Found in Google Analytics 4 → Admin → Data Streams → Web Stream
2. **API Secret**: Google Analytics 4 → Admin → Data Streams → Web Stream → Measurement Protocol → Create

## Usage

### Client-side Tracking (Browser)

```typescript
import { GAClient, createGAConfigFromEnv, Events } from '@repo/analytics';

// Initialize from environment
const config = createGAConfigFromEnv(process.env);
if (config) {
  const client = new GAClient({ config });
  await client.initialize();

  // Track page view
  client.trackPageView({
    page_title: 'Home Page',
    page_location: window.location.href
  });

  // Track custom events
  client.trackEvent(Events.login('email'));
  client.trackEvent(Events.search('remix tutorial'));
}
```

### Server-side Tracking (Cloudflare Workers)

```typescript
import { GAServer, createGAServerFromEnv, getClientId } from '@repo/analytics';

// In your Remix loader/action
export async function loader({ request, context }: LoaderFunctionArgs) {
  const server = createGAServerFromEnv(context.env, getClientId());
  
  if (server) {
    // Track page view
    await server.trackPageView({
      page_title: 'API Response',
      page_location: request.url
    }, request);
  }
  
  return json({ data: 'example' });
}
```

### Privacy Management

```typescript
import { PrivacyManager, ConsentBanner } from '@repo/analytics';

// Initialize privacy manager
const privacyManager = new PrivacyManager({
  analyticsConsent: false, // Start with no consent
  respectDnt: true
});

// Show consent banner
const banner = new ConsentBanner(privacyManager);
if (banner.isConsentNeeded()) {
  banner.show({
    message: 'We use cookies to improve your experience.',
    acceptText: 'Accept All',
    declineText: 'Decline'
  });
}

// Listen for consent changes
privacyManager.onConsentChange((consent) => {
  console.log('Consent updated:', consent);
});
```

### E-commerce Tracking

```typescript
import { EcommerceEvents } from '@repo/analytics';

// Track purchase
const purchaseEvent = {
  transaction_id: 'txn_123',
  currency: 'USD',
  value: 29.99,
  items: [{
    item_id: 'plan_pro',
    item_name: 'Pro Plan',
    item_category: 'subscription',
    price: 29.99,
    quantity: 1
  }]
};

client.trackEcommerce(purchaseEvent);

// Or use utility functions
client.trackEvent(EcommerceEvents.addToCart([item], 'USD'));
```

### SaaS-specific Events

```typescript
import { SaasEvents } from '@repo/analytics';

// Track subscription events
client.trackEvent(SaasEvents.subscriptionStart('Pro Plan', 29.99));
client.trackEvent(SaasEvents.trialStart('Pro Plan', 14));
client.trackEvent(SaasEvents.featureUsage('ai_chat', userId));
```

## API Reference

### Core Classes

- **`GAClient`**: Client-side analytics tracking
- **`GAServer`**: Server-side analytics tracking  
- **`PrivacyManager`**: Privacy and consent management
- **`ConsentBanner`**: Cookie consent UI component

### Utility Functions

- **`Events`**: Common event tracking utilities
- **`EcommerceEvents`**: E-commerce specific events
- **`SaasEvents`**: SaaS business metric events
- **`createGAConfigFromEnv()`**: Create config from environment variables
- **`getClientId()`**: Generate/retrieve client ID

### Configuration Types

- **`GAConfig`**: Main configuration interface
- **`PrivacySettings`**: Privacy preferences
- **`GAEvent`**: Event structure
- **`EcommerceEvent`**: E-commerce event structure

## Privacy Compliance

This package is designed to be privacy-compliant by default:

- ✅ Requires explicit consent before tracking
- ✅ Respects Do Not Track headers
- ✅ Anonymizes IP addresses
- ✅ Provides easy opt-out mechanisms
- ✅ Stores minimal data locally
- ✅ Supports GDPR/CCPA requirements

## Integration with Remix

### Automatic Page View Tracking

```typescript
// In your root.tsx or layout component
import { useEffect } from 'react';
import { useLocation } from '@remix-run/react';
import { GAClient } from '@repo/analytics';

export default function Root() {
  const location = useLocation();

  useEffect(() => {
    // Track page views on route changes
    if (typeof window !== 'undefined' && window.gtag) {
      gtag('config', 'GA_MEASUREMENT_ID', {
        page_path: location.pathname + location.search
      });
    }
  }, [location]);

  return (
    // Your app JSX
  );
}
```

## Development

```bash
# Build the package
pnpm build

# Watch for changes
pnpm dev

# Type check
pnpm type-check
```

## License

Private - Part of the remix-cloudflare-neon-starter monorepo.
