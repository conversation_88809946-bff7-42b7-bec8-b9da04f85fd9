import type { MetaFunction } from "@remix-run/cloudflare";
import { <PERSON> } from "@remix-run/react";
import { Button } from "@repo/ui-kit";
import { CTASection, FeatureGrid, Footer, Header, HeroSection, TeamGrid } from "~/components";
import { aboutConfig } from "~/config/about.config";

export const meta: MetaFunction = () => {
    return [
        { title: "About Us - AI Chat Platform" },
        {
            name: "description",
            content: aboutConfig.hero.description,
        },
    ];
};

export default function About() {
    return (
        <div className="min-h-screen bg-white">
            <Header />

            {/* Hero Section */}
            <HeroSection
                title={aboutConfig.hero.title}
                description={aboutConfig.hero.description}
                primaryCTA={{
                    text: "Try Our Platform",
                    href: "/",
                }}
                secondaryCTA={{
                    text: "Learn More",
                    onClick: () => {
                        document
                            .getElementById("mission-section")
                            ?.scrollIntoView({ behavior: "smooth" });
                    },
                }}
                backgroundGradient="from-blue-50 to-purple-50"
            />

            {/* Mission Section */}
            <section id="mission-section" className="py-20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid md:grid-cols-2 gap-12 items-center">
                        <div>
                            <h2 className="text-3xl font-bold text-gray-900 mb-6">
                                {aboutConfig.mission.title}
                            </h2>
                            <p className="text-lg text-gray-600 mb-6">
                                {aboutConfig.mission.description}
                            </p>
                            <p className="text-lg text-gray-600 mb-6">
                                {aboutConfig.vision.description}
                            </p>
                            <Link to="/landing">
                                <Button className="px-6 py-3">Try Our Platform</Button>
                            </Link>
                        </div>
                        <div className="bg-gray-100 rounded-lg p-8">
                            <div className="space-y-6">
                                <div className="flex items-start">
                                    <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                                        <svg
                                            className="w-6 h-6 text-white"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M13 10V3L4 14h7v7l9-11h-7z"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-gray-900 mb-2">
                                            Innovation First
                                        </h3>
                                        <p className="text-gray-600">
                                            Pushing the boundaries of what's possible with AI
                                            conversation technology.
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start">
                                    <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                                        <svg
                                            className="w-6 h-6 text-white"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-gray-900 mb-2">
                                            User Centered
                                        </h3>
                                        <p className="text-gray-600">
                                            Every feature is designed with our users' needs and
                                            experiences in mind.
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start">
                                    <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
                                        <svg
                                            className="w-6 h-6 text-white"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-gray-900 mb-2">
                                            Trust & Safety
                                        </h3>
                                        <p className="text-gray-600">
                                            Building secure, reliable, and ethical AI systems that
                                            users can depend on.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Team Section */}
            <TeamGrid
                title={aboutConfig.team.title}
                description={aboutConfig.team.description}
                members={aboutConfig.team.members}
                columns={3}
                backgroundColor="bg-gray-50"
            />

            {/* Company Values */}
            <FeatureGrid
                title="Our Values"
                description="The principles that guide everything we do"
                features={aboutConfig.values}
                columns={4}
                backgroundColor="bg-white"
            />

            {/* CTA Section */}
            <CTASection
                title="Join Our Journey"
                description="Help us shape the future of AI-powered conversations"
                primaryCTA={{
                    text: "Try AI Chat",
                    href: "/",
                }}
                secondaryCTA={{
                    text: "View Careers",
                    href: "/careers",
                }}
            />

            <Footer />
        </div>
    );
}
