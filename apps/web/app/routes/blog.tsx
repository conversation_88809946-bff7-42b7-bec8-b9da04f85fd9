import type { MetaFunction } from "@remix-run/cloudflare";
import { Button } from "@repo/ui-kit";
import { CT<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Header, HeroSec<PERSON> } from "~/components";
import { blogCategories, blogConfig, blogPosts } from "~/config/blog.config";

export const meta: MetaFunction = () => {
    return [
        { title: `${blogConfig.title} - AI Chat Platform` },
        {
            name: "description",
            content: blogConfig.description,
        },
    ];
};

export default function Blog() {
    // Get featured post (first post or explicitly marked as featured)
    const featuredPost = blogConfig.featuredPosts[0] || blogPosts[0];

    return (
        <div className="min-h-screen bg-white">
            <Header />

            {/* Hero Section */}
            <HeroSection
                title={blogConfig.title}
                description={blogConfig.description}
                primaryCTA={{
                    text: "Latest Posts",
                    href: "#blog-posts",
                }}
                secondaryCTA={{
                    text: "Subscribe",
                    onClick: () => {
                        document
                            .getElementById("newsletter")
                            ?.scrollIntoView({ behavior: "smooth" });
                    },
                }}
                backgroundGradient="from-blue-50 to-purple-50"
            />

            {/* Category Filter */}
            <section className="py-8 bg-white border-b border-gray-200">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex flex-wrap gap-2 justify-center">
                        {blogCategories.map((category) => (
                            <button
                                key={category.id}
                                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                                    category.id === "all"
                                        ? "bg-blue-600 text-white"
                                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                                }`}
                            >
                                {category.label}
                                {category.count > 0 && (
                                    <span className="ml-1 text-xs">({category.count})</span>
                                )}
                            </button>
                        ))}
                    </div>
                </div>
            </section>

            {/* Featured Post */}
            <section className="py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div className="md:flex">
                            <div className="md:w-1/2">
                                <div
                                    className={`h-64 md:h-full ${featuredPost.image} flex items-center justify-center`}
                                >
                                    <span className="text-4xl font-bold text-white">Featured</span>
                                </div>
                            </div>
                            <div className="md:w-1/2 p-8">
                                <div className="flex items-center mb-4">
                                    <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                        Featured
                                    </span>
                                    <span className="text-gray-500 text-sm ml-3">
                                        {featuredPost.readTime}
                                    </span>
                                </div>
                                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                                    {featuredPost.title}
                                </h2>
                                <p className="text-gray-600 mb-6">{featuredPost.excerpt}</p>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                            <span className="text-white text-sm font-medium">
                                                {featuredPost.author
                                                    .split(" ")
                                                    .map((n) => n[0])
                                                    .join("")}
                                            </span>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">
                                                {featuredPost.author}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {featuredPost.date}
                                            </p>
                                        </div>
                                    </div>
                                    <Button>Read More</Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Blog Posts Grid */}
            <section id="blog-posts" className="py-12 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-8">Latest Posts</h2>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {blogPosts
                            .filter((post) => post.id !== featuredPost.id)
                            .map((post) => (
                                <article
                                    key={post.id}
                                    className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                                >
                                    <div
                                        className={`h-48 ${post.image} flex items-center justify-center`}
                                    >
                                        <span className="text-2xl font-bold text-white">
                                            {post.category}
                                        </span>
                                    </div>
                                    <div className="p-6">
                                        <div className="flex items-center mb-3">
                                            <span className="bg-gray-100 text-gray-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                                {post.category}
                                            </span>
                                            <span className="text-gray-500 text-sm ml-3">
                                                {post.readTime}
                                            </span>
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                                            {post.title}
                                        </h3>
                                        <p className="text-gray-600 mb-4 line-clamp-3">
                                            {post.excerpt}
                                        </p>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center mr-2">
                                                    <span className="text-white text-xs font-medium">
                                                        {post.author
                                                            .split(" ")
                                                            .map((n) => n[0])
                                                            .join("")}
                                                    </span>
                                                </div>
                                                <div>
                                                    <p className="text-xs text-gray-900">
                                                        {post.author}
                                                    </p>
                                                    <p className="text-xs text-gray-500">
                                                        {post.date}
                                                    </p>
                                                </div>
                                            </div>
                                            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                Read More →
                                            </button>
                                        </div>
                                    </div>
                                </article>
                            ))}
                    </div>
                </div>
            </section>

            {/* Newsletter Signup */}
            <div id="newsletter">
                <CTASection
                    title={blogConfig.newsletter.title}
                    description={blogConfig.newsletter.description}
                    primaryCTA={{
                        text: blogConfig.newsletter.buttonText,
                        href: "/newsletter",
                    }}
                    secondaryCTA={{
                        text: "View Archive",
                        href: "/blog/archive",
                    }}
                />
            </div>

            <Footer />
        </div>
    );
}
